package l_explosive_protection

import (
	"activitysrv/internal/dao/dao_explosive_protection"
	"activitysrv/internal/model"
	"context"
	"fmt"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// ExplosiveProtectionLogic 爆护之路业务逻辑
type ExplosiveProtectionLogic struct {
	cycleDAO    *dao_explosive_protection.CycleDAO
	userDataDAO *dao_explosive_protection.UserDataDAO
}

// NewExplosiveProtectionLogic 创建爆护之路业务逻辑实例
func NewExplosiveProtectionLogic() *ExplosiveProtectionLogic {
	return &ExplosiveProtectionLogic{
		cycleDAO:    dao_explosive_protection.NewCycleDAO(),
		userDataDAO: dao_explosive_protection.NewUserDataDAO(),
	}
}

// HandleWeightUpdateEvent 处理入护成功事件
func (logic *ExplosiveProtectionLogic) HandleWeightUpdateEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 1. 配置过滤：检查活动是否开启
	activityCfg := cmodel.GetActivity(model.ActivityIdExplosiveProtection, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		entry.Debugf("爆护之路活动配置不存在")
		return nil
	}

	// 检查活动时间是否有效
	if !logic.isActivityActive(activityCfg) {
		entry.Debugf("爆护之路活动未开启或已过期")
		return nil
	}

	// 检查事件中是否包含活动相关指标
	if !logic.hasActivityMetrics(event, activityCfg) {
		entry.Debugf("事件中不包含爆护之路活动相关指标")
		return nil
	}

	// 2. 周期管理：检查当前周期是否已结束，如果结束则自动创建新周期
	currentCycle, err := logic.cycleDAO.CheckAndCreateCycleIfNeeded(ctx, model.ActivityIdExplosiveProtection, activityCfg.CycleDays)
	if err != nil {
		return fmt.Errorf("检查或创建活动周期失败: %w", err)
	}

	// 3. 指标更新：提取事件中的指标数据并更新
	metricUpdates, err := logic.extractMetricsFromEvent(event, activityCfg)
	if err != nil {
		return fmt.Errorf("提取事件指标失败: %w", err)
	}

	if len(metricUpdates) == 0 {
		entry.Debugf("事件中没有需要更新的指标")
		return nil
	}

	// 4. 原子性更新玩家指标数据
	err = logic.userDataDAO.UpdateUserMetrics(ctx, model.ActivityIdExplosiveProtection, playerId,
		currentCycle.CycleId, metricUpdates, model.MetricOperationAdd)
	if err != nil {
		return fmt.Errorf("更新玩家指标失败: %w", err)
	}

	entry.Infof("成功处理爆护之路事件: playerId=%d, cycleId=%d, metrics=%v",
		playerId, currentCycle.CycleId, metricUpdates)

	return nil
}

// GetActivityProgress 获取活动进度
func (logic *ExplosiveProtectionLogic) GetActivityProgress(ctx context.Context, playerId uint64) (*model.ExplosiveProtectionProgress, error) {
	entry := logx.NewLogEntry(ctx)

	// 获取活动配置
	activityCfg := cmodel.GetActivity(model.ActivityIdExplosiveProtection, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("爆护之路活动配置不存在")
	}

	// 获取当前周期
	currentCycle, err := logic.cycleDAO.CheckAndCreateCycleIfNeeded(ctx, model.ActivityIdExplosiveProtection, activityCfg.CycleDays)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}

	// 获取当前周期的用户数据
	currentUserData, err := logic.userDataDAO.GetUserData(ctx, model.ActivityIdExplosiveProtection, playerId, currentCycle.CycleId)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期用户数据失败: %w", err)
	}

	// 构建进度数据
	progress := &model.ExplosiveProtectionProgress{
		ActivityId:     model.ActivityIdExplosiveProtection,
		CurrentCycleId: currentCycle.CycleId,
		CycleEndTime:   currentCycle.EndTime,
		Metrics:        currentUserData.Metrics,
		ClaimedRecords: currentUserData.GetClaimedStagesList(),
	}

	// 获取上个周期数据（如果存在）
	if currentCycle.CycleId > 1 {
		previousCycleId := currentCycle.CycleId - 1
		previousUserData, err := logic.userDataDAO.GetUserData(ctx, model.ActivityIdExplosiveProtection, playerId, previousCycleId)
		if err != nil {
			entry.Warnf("获取上个周期用户数据失败: %v", err)
		} else {
			progress.PreviousCycle = &model.ExplosiveProtectionProgress{
				ActivityId:     model.ActivityIdExplosiveProtection,
				CurrentCycleId: previousCycleId,
				Metrics:        previousUserData.Metrics,
				ClaimedRecords: previousUserData.GetClaimedStagesList(),
			}
		}
	}

	return progress, nil
}

// ClaimReward 领取奖励
func (logic *ExplosiveProtectionLogic) ClaimReward(ctx context.Context, playerId uint64, cycleId int32, stageId int32) error {
	entry := logx.NewLogEntry(ctx)

	// 获取活动配置
	activityCfg := cmodel.GetActivity(model.ActivityIdExplosiveProtection, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return fmt.Errorf("爆护之路活动配置不存在")
	}

	// 校验活动时间（奖励过期策略：仅保留上一周期）
	currentCycle, err := logic.cycleDAO.GetCurrentCycle(ctx, model.ActivityIdExplosiveProtection)
	if err != nil {
		return fmt.Errorf("获取当前周期失败: %w", err)
	}
	if currentCycle == nil {
		return fmt.Errorf("当前没有活跃的活动周期")
	}

	// 检查周期是否有效（当前周期或上一周期）
	if cycleId != currentCycle.CycleId && cycleId != currentCycle.CycleId-1 {
		return fmt.Errorf("周期 %d 的奖励已过期，仅支持当前周期和上一周期的奖励领取", cycleId)
	}

	// 获取阶段配置
	stageCfg, err := logic.getStageConfig(stageId)
	if err != nil {
		return fmt.Errorf("获取阶段配置失败: %w", err)
	}

	// 获取用户数据
	userData, err := logic.userDataDAO.GetUserData(ctx, model.ActivityIdExplosiveProtection, playerId, cycleId)
	if err != nil {
		return fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 检查是否已领取
	if userData.IsStageClaimedStage(stageId) {
		return fmt.Errorf("阶段 %d 的奖励已经领取过", stageId)
	}

	// 检查是否满足领取条件
	if !logic.checkStageCondition(userData, stageCfg) {
		return fmt.Errorf("不满足阶段 %d 的领取条件", stageId)
	}

	// 原子性领取奖励
	rewardConfig := logic.buildRewardConfig(stageCfg)
	err = logic.userDataDAO.ClaimReward(ctx, model.ActivityIdExplosiveProtection, playerId, cycleId, stageId, rewardConfig)
	if err != nil {
		return fmt.Errorf("领取奖励失败: %w", err)
	}

	entry.Infof("成功领取爆护之路奖励: playerId=%d, cycleId=%d, stageId=%d", playerId, cycleId, stageId)

	// TODO: 调用大厅服发奖接口
	// err = logic.sendRewardToHall(ctx, playerId, stageCfg.Rewards)
	// if err != nil {
	//     entry.Errorf("发放奖励失败: %v", err)
	//     // 记录失败日志用于后续补偿，但不回滚已领取状态
	// }

	return nil
}

// CheckRedDot 检查红点状态
func (logic *ExplosiveProtectionLogic) CheckRedDot(ctx context.Context, playerId uint64) (bool, error) {
	// 获取活动进度
	progress, err := logic.GetActivityProgress(ctx, playerId)
	if err != nil {
		return false, err
	}

	// 获取所有阶段配置
	stageConfigs, err := logic.getAllStageConfigs()
	if err != nil {
		return false, err
	}

	// 检查当前周期是否有可领取的奖励
	hasAvailableReward := logic.checkAvailableRewards(progress.Metrics, progress.ClaimedRecords, stageConfigs)
	if hasAvailableReward {
		return true, nil
	}

	// 检查上个周期是否有可领取的奖励
	if progress.PreviousCycle != nil {
		hasAvailableReward = logic.checkAvailableRewards(progress.PreviousCycle.Metrics,
			progress.PreviousCycle.ClaimedRecords, stageConfigs)
		if hasAvailableReward {
			return true, nil
		}
	}

	return false, nil
}

// isActivityActive 检查活动是否活跃
func (logic *ExplosiveProtectionLogic) isActivityActive(cfg *cmodel.Activity) bool {
	// TODO: 实现活动时间检查逻辑
	// 这里需要根据实际的配置结构来实现
	return true
}

// hasActivityMetrics 检查事件是否包含活动相关指标
func (logic *ExplosiveProtectionLogic) hasActivityMetrics(event *commonPB.EventCommon, cfg *cmodel.Activity) bool {
	// TODO: 根据活动配置检查事件中是否包含相关指标
	// 这里需要根据实际的配置结构来实现
	return true
}

// extractMetricsFromEvent 从事件中提取指标数据
func (logic *ExplosiveProtectionLogic) extractMetricsFromEvent(event *commonPB.EventCommon, cfg *cmodel.Activity) (map[int32]int64, error) {
	metrics := make(map[int32]int64)

	// TODO: 根据事件类型和配置提取相应的指标
	// 这里需要根据实际的事件结构和配置来实现

	// 示例：如果是入护事件，提取鱼重量
	if event.EventType == commonPB.EVENT_TYPE_ET_FISH_GET {
		if weight, exists := event.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)]; exists {
			metrics[model.MetricTypeFishWeight] = weight
			metrics[model.MetricTypeFishCount] = 1

			// 更新最大单次重量
			metrics[model.MetricTypeMaxSingleWeight] = weight
		}
	}

	return metrics, nil
}

// getStageConfig 获取阶段配置
func (logic *ExplosiveProtectionLogic) getStageConfig(stageId int32) (interface{}, error) {
	// TODO: 从配置中获取阶段信息
	// 这里需要根据实际的配置结构来实现
	return nil, nil
}

// checkStageCondition 检查阶段领取条件
func (logic *ExplosiveProtectionLogic) checkStageCondition(userData *model.UserActivityData, stageCfg interface{}) bool {
	// TODO: 根据阶段配置检查用户数据是否满足条件
	// 这里需要根据实际的配置结构来实现
	return true
}

// buildRewardConfig 构建奖励配置
func (logic *ExplosiveProtectionLogic) buildRewardConfig(stageCfg interface{}) string {
	// TODO: 根据阶段配置构建奖励配置JSON
	// 这里需要根据实际的配置结构来实现
	return "{}"
}

// getAllStageConfigs 获取所有阶段配置
func (logic *ExplosiveProtectionLogic) getAllStageConfigs() ([]interface{}, error) {
	// TODO: 从配置中获取所有阶段信息
	// 这里需要根据实际的配置结构来实现
	return []interface{}{}, nil
}

// checkAvailableRewards 检查是否有可领取的奖励
func (logic *ExplosiveProtectionLogic) checkAvailableRewards(metrics map[int32]int64, claimedRecords []int32, stageConfigs []interface{}) bool {
	// TODO: 遍历所有阶段，检查哪些满足条件但尚未领取
	// 这里需要根据实际的配置结构来实现
	return false
}
